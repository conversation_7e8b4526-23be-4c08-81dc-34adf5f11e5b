import { QuestionStats } from '../../types/database';
import { ProcessedQuestion } from '../../types/question';
import { getDatabase } from '../database/init';
import { getWrongQuestions } from '../database/queries';
import { QuestionLoader } from './loader';

export class WrongQuestionsService {
  /**
   * Load wrong questions from database and convert to ProcessedQuestion format
   */
  static async getWrongQuestions(): Promise<ProcessedQuestion[]> {
    try {
      const wrongQuestionStats: QuestionStats[] = await getWrongQuestions();
      
      if (wrongQuestionStats.length === 0) {
        return [];
      }

      // Group wrong questions by volume to load them efficiently
      const volumeQuestionMap: Map<number, Set<number>> = new Map();
      
      wrongQuestionStats.forEach(stat => {
        if (!volumeQuestionMap.has(stat.volume)) {
          volumeQuestionMap.set(stat.volume, new Set());
        }
        volumeQuestionMap.get(stat.volume)!.add(stat.question_id);
      });

      // Load all questions from each volume and filter wrong ones
      const wrongQuestions: ProcessedQuestion[] = [];
      
      for (const [volume, questionIds] of volumeQuestionMap) {
        const volumeQuestions = await QuestionLoader.loadVolume(volume);
        const volumeWrongQuestions = volumeQuestions.filter(q => questionIds.has(q.id));
        
        wrongQuestions.push(...volumeWrongQuestions);
      }

      // Sort by last attempted (most recent first)
      const questionStatsMap = new Map(wrongQuestionStats.map(stat => [stat.question_id, stat]));
      
      wrongQuestions.sort((a, b) => {
        const aLastAttempted = questionStatsMap.get(a.id)?.last_attempted;
        const bLastAttempted = questionStatsMap.get(b.id)?.last_attempted;
        
        if (!aLastAttempted || !bLastAttempted) return 0;
        
        return new Date(bLastAttempted).getTime() - new Date(aLastAttempted).getTime();
      });

      console.log(`Loaded ${wrongQuestions.length} wrong questions for review`);
      return wrongQuestions;
    } catch (error) {
      console.error('Failed to get wrong questions:', error);
      throw error;
    }
  }

  /**
   * Mark a question as no longer needing review (when answered correctly in review mode)
   */
  static async markQuestionAsReviewed(questionId: number, volume: number): Promise<void> {
    const db = getDatabase();

    try {
      // Set need_review = 0 when question is answered correctly in review mode
      // This preserves the historical wrong_attempts count while removing from review queue
      await db.runAsync(
        `UPDATE question_stats
         SET need_review = 0
         WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      );
    } catch (error) {
      console.error('Failed to mark question as reviewed:', error);
      throw error;
    }
  }

  /**
   * Mark a question as needing review (when answered incorrectly in practice/exam)
   */
  static async markQuestionForReview(questionId: number, volume: number): Promise<void> {
    const db = getDatabase();

    try {
      // Set need_review = 1 when question is answered incorrectly
      await db.runAsync(
        `UPDATE question_stats
         SET need_review = 1
         WHERE question_id = ? AND volume = ?`,
        [questionId, volume]
      );
    } catch (error) {
      console.error('Failed to mark question for review:', error);
      throw error;
    }
  }

  /**
   * Get questions that need review (need_review = 1)
   */
  static async getActiveWrongQuestions(): Promise<ProcessedQuestion[]> {
    const db = getDatabase();

    try {
      // Get questions that need review using the new need_review flag
      const result = await db.getAllAsync(
        `SELECT question_id, volume FROM question_stats WHERE need_review = 1 ORDER BY last_attempted DESC`
      ) as { question_id: number; volume: number }[];

      if (result.length === 0) {
        return [];
      }

      // Group questions by volume to load them efficiently
      const volumeQuestionMap: Map<number, Set<number>> = new Map();

      result.forEach(row => {
        if (!volumeQuestionMap.has(row.volume)) {
          volumeQuestionMap.set(row.volume, new Set());
        }
        volumeQuestionMap.get(row.volume)!.add(row.question_id);
      });

      // Load all questions from each volume and filter review ones
      const reviewQuestions: ProcessedQuestion[] = [];

      for (const [volume, questionIds] of volumeQuestionMap) {
        const volumeQuestions = await QuestionLoader.loadVolume(volume);
        const volumeReviewQuestions = volumeQuestions.filter(q => questionIds.has(q.id));

        reviewQuestions.push(...volumeReviewQuestions);
      }

      return reviewQuestions;
    } catch (error) {
      console.error('Failed to load questions for review:', error);
      throw error;
    }
  }

  /**
   * Reset review status for all questions (for when user wants to review all wrong questions again)
   */
  static async resetReviewStatus(): Promise<void> {
    const db = getDatabase();

    try {
      // Set need_review = 1 for all questions that have wrong_attempts > 0
      await db.runAsync(
        `UPDATE question_stats
         SET need_review = 1
         WHERE wrong_attempts > 0`
      );
    } catch (error) {
      console.error('Failed to reset review status:', error);
      throw error;
    }
  }

  /**
   * Get wrong questions count for display
   */
  static async getWrongQuestionsCount(): Promise<number> {
    const db = getDatabase();
    
    try {
      const result = await db.getFirstAsync(
        `SELECT COUNT(*) as count FROM question_stats WHERE wrong_attempts > 0`
      ) as { count: number };
      
      return result?.count || 0;
    } catch (error) {
      console.error('Failed to get wrong questions count:', error);
      return 0;
    }
  }

  /**
   * Debug utility to inspect question_stats table
   */
  static async debugQuestionStats(): Promise<void> {
    const db = getDatabase();
    
    try {
      console.log('\n=== Question Stats Debug ===');
      
      // Get all records with wrong attempts
      const wrongStats = await db.getAllAsync(
        `SELECT question_id, volume, wrong_attempts, total_attempts, last_attempted 
         FROM question_stats 
         WHERE wrong_attempts > 0 
         ORDER BY volume, question_id`
      ) as { question_id: number; volume: number; wrong_attempts: number; total_attempts: number; last_attempted: string }[];
      
      console.log(`Total wrong questions in database: ${wrongStats.length}`);
      
      const volumeBreakdown = new Map<number, number>();
      wrongStats.forEach(stat => {
        const count = volumeBreakdown.get(stat.volume) || 0;
        volumeBreakdown.set(stat.volume, count + 1);
        
        console.log(`  Question ${stat.question_id} (Volume ${stat.volume}): ${stat.wrong_attempts} wrong out of ${stat.total_attempts} attempts`);
      });
      
      console.log('\nVolume breakdown:');
      for (const [volume, count] of volumeBreakdown) {
        console.log(`  Volume ${volume}: ${count} wrong questions`);
      }
      
      console.log('=== End Debug ===\n');
    } catch (error) {
      console.error('Failed to debug question stats:', error);
    }
  }
} 