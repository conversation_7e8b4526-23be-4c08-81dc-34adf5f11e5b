import * as SQLite from 'expo-sqlite';
import { getDatabaseConfig, getRetryConfig, isAndroid, supportsAdvancedSQLite } from '../../utils/platform';
import { CREATE_INDEXES, CREATE_TABLES } from './schema';

let database: SQLite.SQLiteDatabase | null = null;
let isInitializing = false;
let initializationPromise: Promise<void> | null = null;

export function getDatabase(): SQLite.SQLiteDatabase {
  if (!database) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return database;
}

export async function initializeDatabase(): Promise<void> {
  // Prevent multiple concurrent initializations
  if (isInitializing && initializationPromise) {
    console.log('Database initialization already in progress, waiting...');
    return initializationPromise;
  }

  if (database) {
    console.log('Database already initialized');
    return;
  }

  isInitializing = true;
  initializationPromise = _performInitialization();

  try {
    await initializationPromise;
  } finally {
    isInitializing = false;
    initializationPromise = null;
  }
}

async function _performInitialization(): Promise<void> {
  const retryConfig = getRetryConfig();
  let lastError: Error | null = null;

  console.log(`Starting database initialization on ${isAndroid ? 'Android' : 'iOS'}...`);

  // For Android, try ultra-conservative approach first
  if (isAndroid) {
    try {
      await _initializeDatabaseUltraConservative();
      console.log('✅ Android ultra-conservative initialization succeeded');
      return;
    } catch (error) {
      console.warn('❌ Android ultra-conservative initialization failed:', error);
      lastError = error as Error;
    }
  }

  // Try standard initialization with retries
  for (let attempt = 1; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      await _initializeDatabaseInternal();
      console.log(`✅ Database initialization succeeded on attempt ${attempt}`);
      return;
    } catch (error) {
      lastError = error as Error;
      console.warn(`❌ Database initialization attempt ${attempt}/${retryConfig.maxRetries} failed:`, error);
      
      // Clean up failed attempt
      if (database) {
        try {
          await database.closeAsync();
        } catch {}
        database = null;
      }

      if (attempt === retryConfig.maxRetries) {
        console.error('All standard initialization attempts failed, trying fallback...');
        try {
          await _initializeDatabaseFallback();
          console.log('✅ Database fallback initialization succeeded');
          return;
        } catch (fallbackError) {
          console.error('❌ Database fallback initialization also failed:', fallbackError);
          
          // Final attempt - try minimal database
          try {
            await _initializeDatabaseMinimal();
            console.log('✅ Database minimal initialization succeeded');
            return;
          } catch (minimalError) {
            console.error('❌ All database initialization methods failed');
            throw lastError || fallbackError || minimalError;
          }
        }
      }

      // Wait before retry with exponential backoff
      const delay = Math.min(
        retryConfig.initialDelay * Math.pow(retryConfig.backoffMultiplier, attempt - 1),
        retryConfig.maxDelay
      );
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError || new Error('Database initialization failed');
}

/**
 * Internal database initialization with platform-specific optimizations
 */
async function _initializeDatabaseInternal(): Promise<void> {
  try {
    database = await SQLite.openDatabaseAsync('macau_driving_test.db');
    const dbConfig = getDatabaseConfig();
    
    // Apply platform-specific PRAGMA settings
    await _applyDatabasePragmas(database, dbConfig);
    
    // Simplified migration for Android compatibility
    if (supportsAdvancedSQLite()) {
      try {
        await migrateQuestionStatsTable();
        await runMigrations();
      } catch (migrationError) {
        console.warn('Migration failed, continuing with existing schema:', migrationError);
      }
    } else {
      // Simplified migration for Android
      try {
        await _simpleMigrationCheck();
        await runMigrations();
      } catch (migrationError) {
        console.warn('Simple migration check failed, continuing:', migrationError);
      }
    }
    
    // Create tables and indexes with better error handling
    await _createTablesAndIndexes();
    
    // Initialize volume progress
    await initializeVolumeProgress(database);
    
    console.log('Database initialized successfully');
  } catch (error) {
    // Clean up on failure
    if (database) {
      try {
        await database.closeAsync();
      } catch {}
      database = null;
    }
    throw error;
  }
}

/**
 * Fallback initialization with minimal features for maximum compatibility
 */
async function _initializeDatabaseFallback(): Promise<void> {
  try {
    console.log('Attempting fallback database initialization...');
    database = await SQLite.openDatabaseAsync('macau_driving_test.db');
    
    // Minimal PRAGMA settings for compatibility
    await database.execAsync('PRAGMA foreign_keys = ON;');
    await database.execAsync('PRAGMA journal_mode = DELETE;'); // Most compatible mode
    await database.execAsync('PRAGMA synchronous = NORMAL;');
    
    // Skip migration entirely in fallback mode
    console.warn('Skipping migration in fallback mode');
    
    // Create tables with simplified approach
    try {
      await database.execAsync(CREATE_TABLES);
    } catch (error) {
      console.warn('Table creation failed, attempting individual table creation:', error);
      await _createTablesIndividually();
    }
    
    try {
      await database.execAsync(CREATE_INDEXES);
    } catch (error) {
      console.warn('Index creation failed:', error);
      // Indexes are optional, continue without them
    }
    
    // Initialize volume progress
    await initializeVolumeProgress(database);
    
    console.log('Database initialized successfully using fallback method');
  } catch (error) {
    if (database) {
      try {
        await database.closeAsync();
      } catch {}
      database = null;
    }
    throw error;
  }
}

/**
 * Ultra-conservative Android database initialization - minimal features for maximum compatibility
 */
async function _initializeDatabaseUltraConservative(): Promise<void> {
  try {
    console.log('🔧 Attempting ultra-conservative Android initialization...');
    
    // Use basic database opening without any special flags
    database = await SQLite.openDatabaseAsync('macau_driving_test.db');
    
    // Validate database connection
    await _validateDatabaseConnection(database);
    
    // Apply only the most essential PRAGMA settings one by one with error handling
    try {
      await database.execAsync('PRAGMA foreign_keys = ON;');
    } catch (error) {
      console.warn('Failed to enable foreign keys, continuing without them:', error);
    }
    
    // Skip complex migration for ultra-conservative mode, but run essential migrations
    console.log('📋 Skipping complex migration in ultra-conservative mode');

    // Create tables with the most basic approach - one by one
    await _createTablesOneByOne();

    // Run essential migrations even in ultra-conservative mode
    try {
      await _runEssentialMigrations(database);
      console.log('✅ Essential migrations completed in ultra-conservative mode');
    } catch (migrationError) {
      console.warn('⚠️ Essential migrations failed in ultra-conservative mode:', migrationError);
      // Continue anyway - this might just be a timing issue
    }
    
    // Skip indexes in ultra-conservative mode - they're not essential
    console.log('📋 Skipping indexes in ultra-conservative mode for maximum compatibility');
    
    // Initialize volume progress
    await _initializeVolumeProgressSafe(database);
    
    console.log('✅ Ultra-conservative database initialization completed');
  } catch (error) {
    if (database) {
      try {
        await database.closeAsync();
      } catch {}
      database = null;
    }
    throw error;
  }
}

/**
 * Minimal database initialization - absolute bare minimum for app functionality
 */
async function _initializeDatabaseMinimal(): Promise<void> {
  try {
    console.log('🆘 Attempting minimal database initialization...');
    
    database = await SQLite.openDatabaseAsync('macau_driving_test.db');
    
    // Validate connection
    await _validateDatabaseConnection(database);
    
    // Only create the most essential table - answer_records
    await database.execAsync(`
      CREATE TABLE IF NOT EXISTS answer_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question_id INTEGER NOT NULL,
        volume INTEGER NOT NULL,
        chapter INTEGER NOT NULL,
        is_correct BOOLEAN NOT NULL,
        mode TEXT NOT NULL,
        session_id TEXT NOT NULL,
        selected_option TEXT,
        correct_option TEXT,
        time_spent INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Create sessions table for basic functionality
    await database.execAsync(`
      CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT,
        total_questions INTEGER,
        correct_count INTEGER DEFAULT 0,
        wrong_count INTEGER DEFAULT 0,
        duration_seconds INTEGER,
        volumes TEXT,
        config TEXT,
        is_completed BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME
      );
    `);
    
    // Create volume_progress for basic progress tracking
    await database.execAsync(`
      CREATE TABLE IF NOT EXISTS volume_progress (
        volume INTEGER PRIMARY KEY,
        total_questions INTEGER,
        seen_count INTEGER DEFAULT 0,
        correct_count INTEGER DEFAULT 0,
        wrong_count INTEGER DEFAULT 0,
        last_practice DATETIME,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Skip question_stats table - it's causing the most problems
    console.log('⚠️ Skipping question_stats table in minimal mode');
    
    // Initialize volume progress
    await _initializeVolumeProgressSafe(database);
    
    console.log('✅ Minimal database initialization completed - limited functionality');
  } catch (error) {
    if (database) {
      try {
        await database.closeAsync();
      } catch {}
      database = null;
    }
    throw error;
  }
}

/**
 * Validate database connection with a simple query
 */
async function _validateDatabaseConnection(db: SQLite.SQLiteDatabase): Promise<void> {
  try {
    const result = await db.getAllAsync('SELECT 1 as test');
    if (!result || result.length === 0) {
      throw new Error('Database connection validation failed - no results');
    }
    console.log('✅ Database connection validated');
  } catch (error) {
    console.error('❌ Database connection validation failed:', error);
    throw new Error(`Database connection is not working: ${error}`);
  }
}

/**
 * Create tables one by one with individual error handling
 */
async function _createTablesOneByOne(): Promise<void> {
  const tables = [
    {
      name: 'answer_records',
      sql: `CREATE TABLE IF NOT EXISTS answer_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        question_id INTEGER NOT NULL,
        volume INTEGER NOT NULL,
        chapter INTEGER NOT NULL,
        is_correct BOOLEAN NOT NULL,
        mode TEXT NOT NULL,
        session_id TEXT NOT NULL,
        selected_option TEXT,
        correct_option TEXT,
        time_spent INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );`
    },
    {
      name: 'sessions',
      sql: `CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT,
        total_questions INTEGER,
        correct_count INTEGER DEFAULT 0,
        wrong_count INTEGER DEFAULT 0,
        duration_seconds INTEGER,
        volumes TEXT,
        config TEXT,
        is_completed BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME
      );`
    },
    {
      name: 'volume_progress',
      sql: `CREATE TABLE IF NOT EXISTS volume_progress (
        volume INTEGER PRIMARY KEY,
        total_questions INTEGER,
        seen_count INTEGER DEFAULT 0,
        correct_count INTEGER DEFAULT 0,
        wrong_count INTEGER DEFAULT 0,
        last_practice DATETIME,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );`
    },
    {
      name: 'question_stats',
      sql: `CREATE TABLE IF NOT EXISTS question_stats (
        question_id INTEGER NOT NULL,
        volume INTEGER NOT NULL,
        total_attempts INTEGER DEFAULT 0,
        correct_attempts INTEGER DEFAULT 0,
        wrong_attempts INTEGER DEFAULT 0,
        last_attempted DATETIME,
        is_bookmarked BOOLEAN DEFAULT 0,
        note TEXT,
        need_review BOOLEAN DEFAULT 0,
        PRIMARY KEY (question_id, volume)
      );`
    }
  ];

  for (const table of tables) {
    try {
      await database!.execAsync(table.sql);
      console.log(`✅ Created table: ${table.name}`);
    } catch (error) {
      console.error(`❌ Failed to create table ${table.name}:`, error);
      if (table.name === 'answer_records' || table.name === 'sessions') {
        // These are essential tables, fail if we can't create them
        throw error;
      }
      // Continue without non-essential tables
    }
  }
}

/**
 * Safe volume progress initialization
 */
async function _initializeVolumeProgressSafe(db: SQLite.SQLiteDatabase): Promise<void> {
  try {
    for (let volume = 1; volume <= 5; volume++) {
      await db.runAsync(
        `INSERT OR IGNORE INTO volume_progress (volume, total_questions) VALUES (?, ?)`,
        [volume, 0]
      );
    }
    console.log('✅ Volume progress initialized');
  } catch (error) {
    console.warn('⚠️ Failed to initialize volume progress, continuing without it:', error);
    // Not critical, continue without it
  }
}

/**
 * Apply platform-specific database PRAGMA settings
 */
async function _applyDatabasePragmas(db: SQLite.SQLiteDatabase, config: any): Promise<void> {
  const pragmas = [
    { name: 'journal_mode', value: config.journalMode },
    { name: 'synchronous', value: config.synchronous },
    { name: 'page_size', value: config.pageSize },
    { name: 'cache_size', value: config.cacheSize },
    { name: 'busy_timeout', value: config.busyTimeout }
  ];
  
  // Apply PRAGMA settings one by one with individual error handling
  for (const pragma of pragmas) {
    try {
      await db.execAsync(`PRAGMA ${pragma.name} = ${pragma.value};`);
    } catch (error) {
      console.warn(`Failed to set PRAGMA ${pragma.name}:`, error);
      // Continue with other settings
    }
  }
  
  // Handle foreign keys separately
  if (config.enableForeignKeys) {
    try {
      await db.execAsync('PRAGMA foreign_keys = ON;');
    } catch (error) {
      console.warn('Failed to enable foreign keys:', error);
    }
  }
  
  console.log(`Applied ${isAndroid ? 'Android' : 'iOS'} database configuration`);
}

/**
 * Create tables and indexes with better error handling
 */
async function _createTablesAndIndexes(): Promise<void> {
  try {
    await database!.execAsync(CREATE_TABLES);
    await database!.execAsync(CREATE_INDEXES);
  } catch (error) {
    console.warn('Batch table/index creation failed, trying individual creation:', error);
    await _createTablesIndividually();
    await _createIndexesIndividually();
  }
}

/**
 * Create tables individually for better error isolation
 */
async function _createTablesIndividually(): Promise<void> {
  const tables = [
    `CREATE TABLE IF NOT EXISTS answer_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      question_id INTEGER NOT NULL,
      volume INTEGER NOT NULL,
      chapter INTEGER NOT NULL,
      is_correct BOOLEAN NOT NULL,
      mode TEXT NOT NULL,
      session_id TEXT NOT NULL,
      selected_option TEXT,
      correct_option TEXT,
      time_spent INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );`,
    
    `CREATE TABLE IF NOT EXISTS sessions (
      id TEXT PRIMARY KEY,
      type TEXT NOT NULL,
      title TEXT,
      total_questions INTEGER,
      correct_count INTEGER DEFAULT 0,
      wrong_count INTEGER DEFAULT 0,
      duration_seconds INTEGER,
      volumes TEXT,
      config TEXT,
      is_completed BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      completed_at DATETIME
    );`,
    
    `CREATE TABLE IF NOT EXISTS volume_progress (
      volume INTEGER PRIMARY KEY,
      total_questions INTEGER,
      seen_count INTEGER DEFAULT 0,
      correct_count INTEGER DEFAULT 0,
      wrong_count INTEGER DEFAULT 0,
      last_practice DATETIME,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );`,
    
    `CREATE TABLE IF NOT EXISTS question_stats (
      question_id INTEGER NOT NULL,
      volume INTEGER NOT NULL,
      total_attempts INTEGER DEFAULT 0,
      correct_attempts INTEGER DEFAULT 0,
      wrong_attempts INTEGER DEFAULT 0,
      last_attempted DATETIME,
      is_bookmarked BOOLEAN DEFAULT 0,
      note TEXT,
      need_review BOOLEAN DEFAULT 0,
      PRIMARY KEY (question_id, volume)
    );`
  ];

  for (const [index, tableSQL] of tables.entries()) {
    try {
      await database!.execAsync(tableSQL);
    } catch (error) {
      console.error(`Failed to create table ${index + 1}:`, error);
      throw error;
    }
  }
}

/**
 * Create indexes individually for better error isolation
 */
async function _createIndexesIndividually(): Promise<void> {
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_records_session ON answer_records(session_id);',
    'CREATE INDEX IF NOT EXISTS idx_records_question ON answer_records(question_id);',
    'CREATE INDEX IF NOT EXISTS idx_records_created ON answer_records(created_at);',
    'CREATE INDEX IF NOT EXISTS idx_sessions_type ON sessions(type);',
    'CREATE INDEX IF NOT EXISTS idx_sessions_created ON sessions(created_at DESC);',
    'CREATE INDEX IF NOT EXISTS idx_question_stats_volume ON question_stats(volume);'
  ];

  for (const indexSQL of indexes) {
    try {
      await database!.execAsync(indexSQL);
    } catch (error) {
      console.warn('Failed to create index:', indexSQL, error);
      // Indexes are optional, continue
    }
  }
}

/**
 * Simple migration check for Android compatibility
 */
async function _simpleMigrationCheck(): Promise<void> {
  try {
    // Just check if the table exists and has the expected structure
    const tableInfo = await database!.getAllAsync(`PRAGMA table_info(question_stats)`) as any[];
    
    if (tableInfo.length === 0) {
      console.log('question_stats table does not exist yet, no migration needed');
      return;
    }
    
    // Check if we have composite primary key (simple check)
    const primaryKeys = tableInfo.filter(col => col.pk > 0);
    if (primaryKeys.length > 1) {
      console.log('question_stats table already has composite primary key');
      return;
    }
    
    console.log('Simple migration check completed');
  } catch (error) {
    console.warn('Simple migration check failed:', error);
    throw error;
  }
}

/**
 * Reset database completely (for debugging purposes)
 * WARNING: This will delete all user data!
 */
export async function resetDatabase(): Promise<void> {
  try {
    if (database) {
      await database.closeAsync();
      database = null;
    }
    
    // Delete the database file and reinitialize
    const db = await SQLite.openDatabaseAsync('macau_driving_test.db');
    await db.execAsync('DROP TABLE IF EXISTS question_stats;');
    await db.execAsync('DROP TABLE IF EXISTS question_stats_backup;');
    await db.execAsync('DROP TABLE IF EXISTS answer_records;');
    await db.execAsync('DROP TABLE IF EXISTS sessions;');
    await db.execAsync('DROP TABLE IF EXISTS volume_progress;');
    await db.closeAsync();
    
    console.log('Database reset completed');
    
    // Reinitialize
    await initializeDatabase();
  } catch (error) {
    console.error('Failed to reset database:', error);
    throw error;
  }
}

/**
 * Run database migrations for new features
 */
async function runMigrations(): Promise<void> {
  try {
    console.log('Running database migrations...');

    // Check if need_review column exists
    const tableInfo = await database!.getAllAsync(`PRAGMA table_info(question_stats)`) as any[];
    const hasNeedReviewColumn = tableInfo.some(col => col.name === 'need_review');

    if (!hasNeedReviewColumn) {
      console.log('Adding need_review column to question_stats table...');

      // Add the need_review column
      await database!.execAsync('ALTER TABLE question_stats ADD COLUMN need_review BOOLEAN DEFAULT 0;');

      // Migrate existing data: Set need_review = 1 for questions with wrong_attempts > 0
      await database!.execAsync('UPDATE question_stats SET need_review = 1 WHERE wrong_attempts > 0;');

      // Reset old mastered_in_review flags
      await database!.execAsync(`UPDATE question_stats SET is_bookmarked = 0, note = NULL WHERE note = 'mastered_in_review';`);

      console.log('Migration completed: need_review column added and data migrated');
    } else {
      console.log('need_review column already exists, skipping migration');
    }

  } catch (error) {
    console.error('Migration failed:', error);
    // Don't throw error - continue with existing schema
  }
}

/**
 * Run essential migrations that must work even in ultra-conservative mode
 */
async function _runEssentialMigrations(db: SQLite.SQLiteDatabase): Promise<void> {
  try {
    console.log('Running essential migrations...');

    // Check if question_stats table exists
    const tableExists = await db.getFirstAsync(
      `SELECT name FROM sqlite_master WHERE type='table' AND name='question_stats'`
    );

    if (!tableExists) {
      console.log('question_stats table does not exist, skipping essential migrations');
      return;
    }

    // Check if need_review column exists
    const tableInfo = await db.getAllAsync(`PRAGMA table_info(question_stats)`) as any[];
    const hasNeedReviewColumn = tableInfo.some(col => col.name === 'need_review');

    if (!hasNeedReviewColumn) {
      console.log('Adding need_review column to question_stats table...');

      try {
        // Add the need_review column
        await db.execAsync('ALTER TABLE question_stats ADD COLUMN need_review BOOLEAN DEFAULT 0;');

        // Migrate existing data: Set need_review = 1 for questions with wrong_attempts > 0
        await db.execAsync('UPDATE question_stats SET need_review = 1 WHERE wrong_attempts > 0;');

        // Reset old mastered_in_review flags
        await db.execAsync(`UPDATE question_stats SET is_bookmarked = 0, note = NULL WHERE note = 'mastered_in_review';`);

        console.log('Essential migration completed: need_review column added and data migrated');
      } catch (alterError) {
        console.warn('Failed to add need_review column, this might be expected if column already exists:', alterError);
        // Continue anyway - the column might already be there from table creation
      }
    } else {
      console.log('need_review column already exists, skipping essential migration');
    }

  } catch (error) {
    console.error('Essential migrations failed:', error);
    // Don't throw - continue with app startup
  }
}

/**
 * Migrate question_stats table from single primary key to composite primary key
 */
async function migrateQuestionStatsTable(): Promise<void> {
  try {
    // Check if the table exists and get its schema
    const tableInfo = await database!.getAllAsync(
      `PRAGMA table_info(question_stats)`
    ) as any[];
    
    if (tableInfo.length === 0) {
      console.log('question_stats table does not exist yet, no migration needed');
      return;
    }
    
    // Check if we already have composite primary key
    const primaryKeys = tableInfo.filter(col => col.pk > 0);
    if (primaryKeys.length > 1) {
      // console.log('question_stats table already has composite primary key, no migration needed');
      return;
    }
    
    console.log('Attempting question_stats table migration...');
    
    // Use a single transaction for the entire migration with timeout
    await Promise.race([
      database!.withTransactionAsync(async () => {
        // Step 1: Create backup table
        await database!.execAsync(`
          CREATE TABLE question_stats_backup AS 
          SELECT * FROM question_stats;
        `);
        
        // Step 2: Drop the old table
        await database!.execAsync('DROP TABLE question_stats;');
        
        // Step 3: Create new table with composite primary key
        await database!.execAsync(`
          CREATE TABLE question_stats (
            question_id INTEGER NOT NULL,
            volume INTEGER NOT NULL,
            total_attempts INTEGER DEFAULT 0,
            correct_attempts INTEGER DEFAULT 0,
            wrong_attempts INTEGER DEFAULT 0,
            last_attempted DATETIME,
            is_bookmarked BOOLEAN DEFAULT 0,
            note TEXT,
            PRIMARY KEY (question_id, volume)
          );
        `);
        
        // Step 4: Insert data back, handling potential duplicates
        await database!.execAsync(`
          INSERT OR REPLACE INTO question_stats 
          (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted, is_bookmarked, note)
          SELECT 
            question_id, 
            volume,
            SUM(total_attempts) as total_attempts,
            SUM(correct_attempts) as correct_attempts, 
            SUM(wrong_attempts) as wrong_attempts,
            MAX(last_attempted) as last_attempted,
            MAX(is_bookmarked) as is_bookmarked,
            MAX(note) as note
          FROM question_stats_backup 
          GROUP BY question_id, volume;
        `);
        
        // Step 5: Drop backup table
        await database!.execAsync('DROP TABLE question_stats_backup;');
      }),
      // Timeout after 10 seconds
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Migration timeout')), 10000)
      )
    ]);
    
    console.log('question_stats table migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    
    // Try to restore from backup if it exists
    try {
      const backupExists = await database!.getFirstAsync(
        `SELECT name FROM sqlite_master WHERE type='table' AND name='question_stats_backup'`
      );
      
      if (backupExists) {
        console.log('Restoring from backup...');
        await database!.execAsync('DROP TABLE IF EXISTS question_stats;');
        await database!.execAsync('ALTER TABLE question_stats_backup RENAME TO question_stats;');
        console.log('Restored from backup successfully');
      }
    } catch {
      console.warn('Could not restore from backup, will continue with existing schema');
    }
    
    // Re-throw the error to be caught by the caller
    throw error;
  }
}

async function initializeVolumeProgress(db: SQLite.SQLiteDatabase) {
  for (let volume = 1; volume <= 5; volume++) {
    await db.runAsync(
      `INSERT OR IGNORE INTO volume_progress (volume, total_questions) VALUES (?, ?)`,
      [volume, 0]
    );
  }
}