import React from 'react';
import {
    Modal as RNModal,
    Text,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import { Button } from './Button';

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  primaryAction?: {
    title: string;
    onPress: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };
  secondaryAction?: {
    title: string;
    onPress: () => void;
  };
  dismissible?: boolean;
}

export function Modal({
  visible,
  onClose,
  title,
  children,
  primaryAction,
  secondaryAction,
  dismissible = true,
}: ModalProps) {
  const handleBackdropPress = () => {
    if (dismissible) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View className="flex-1 bg-black/50 justify-center items-center p-5">
          <TouchableWithoutFeedback>
            <View className="bg-white rounded-2xl max-w-[400px] w-full shadow-lg" style={{ maxHeight: '80%' }}>
              {title && (
                <View style={{ paddingTop: 24, paddingHorizontal: 24, paddingBottom: 16, borderBottomWidth: 1, borderBottomColor: '#e5e5ea' }}>
                  <Text style={{ fontSize: 20, fontWeight: '600', color: '#1f2937', textAlign: 'center' }}>{title}</Text>
                </View>
              )}

              <View style={{ padding: 24 }}>
                {children}
              </View>

              {(primaryAction || secondaryAction) && (
                <View style={{ flexDirection: 'row', paddingHorizontal: 24, paddingBottom: 24, gap: 12 }}>
                  {secondaryAction && (
                    <Button
                      title={secondaryAction.title}
                      onPress={secondaryAction.onPress}
                      variant="outline"
                      style={{ flex: 1 }}
                    />
                  )}
                  {primaryAction && (
                    <Button
                      title={primaryAction.title}
                      onPress={primaryAction.onPress}
                      variant={primaryAction.variant || 'primary'}
                      style={{ flex: 1 }}
                    />
                  )}
                </View>
              )}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
}