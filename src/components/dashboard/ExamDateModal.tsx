import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import React, { useEffect, useState } from 'react';
import { Platform, Text, View } from 'react-native';
import { usePreferencesStore } from '../../store/usePreferencesStore';
import { Modal } from '../common/Modal';

interface ExamDateModalProps {
  visible: boolean;
  onClose: () => void;
}

export function ExamDateModal({ visible, onClose }: ExamDateModalProps) {
  const { examDate, updateExamDate } = usePreferencesStore();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  useEffect(() => {
    if (visible) {
      if (examDate) {
        const examDateObj = new Date(examDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const maxDate = new Date();
        maxDate.setFullYear(maxDate.getFullYear() + 1);

        const examDateNormalized = new Date(examDateObj);
        examDateNormalized.setHours(0, 0, 0, 0);

        // Only set stored date if it's within valid range, otherwise set to today
        if (examDateNormalized >= today && examDateNormalized <= maxDate) {
          setSelectedDate(examDateObj);
        } else {
          setSelectedDate(new Date()); // Default to today if stored date is invalid
        }
      } else {
        setSelectedDate(new Date()); // Default to today if no exam date set
      }
    }
  }, [visible, examDate]);

  const handleDateChange = (_event: DateTimePickerEvent, date?: Date) => {
    if (date) {
      // Validate date is within allowed range (today to 1 year from today)
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() + 1);
      maxDate.setHours(23, 59, 59, 999);

      const selectedDateNormalized = new Date(date);
      selectedDateNormalized.setHours(0, 0, 0, 0);

      // Only update if date is within valid range
      if (selectedDateNormalized >= today && selectedDateNormalized <= maxDate) {
        setSelectedDate(date);
      }
      // If date is outside range, don't update (picker should prevent this, but this is a safeguard)
    }
  };

  const handleSave = async () => {
    try {
      const dateString = selectedDate ? selectedDate.toISOString().split('T')[0] : null;
      await updateExamDate(dateString);
      onClose();
    } catch (error) {
      console.error('Failed to save exam date:', error);
    }
  };

  const handleClear = async () => {
    try {
      await updateExamDate(null);
      setSelectedDate(null);
    } catch (error) {
      console.error('Failed to clear exam date:', error);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return '請選擇日期';
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Calculate maximum date (1 year from today)
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 1);

  return (
    <>
      <Modal
        visible={visible}
        onClose={onClose}
        title="設定考試日期"
        primaryAction={{
          title: '儲存',
          onPress: handleSave,
        }}
        secondaryAction={{
          title: '清除',
          onPress: handleClear,
        }}
      >
        <View style={{ gap: 20 }}>
          {/* Date Picker */}
          <View className="items-center">
            <DateTimePicker
              value={selectedDate || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              minimumDate={new Date()}
              maximumDate={maxDate}
              onChange={handleDateChange}
              style={Platform.OS === 'ios' ? { width: '100%', height: 200 } : undefined}
            />
          </View>

          {/* Current Selection Display */}
          <View className="bg-gray-50 rounded-lg p-4">
            <Text className="text-base font-medium text-gray-900 text-center">
              選擇的日期：{formatDate(selectedDate)}
            </Text>
          </View>
        </View>
      </Modal>
    </>
  );
}

