import { Ionicons } from '@expo/vector-icons';
import { router, useFocusEffect } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import {
    ActionButtons,
    ExamDateModal,
    TopStatusDashboard
} from '../src/components/dashboard';
import VolumePracticeCard from '../src/components/dashboard/VolumePracticeCard';
import { useDatabase } from '../src/hooks/useDatabase';
import { useStatistics } from '../src/hooks/useStatistics';
import { useWrongQuestionsCount } from '../src/hooks/useWrongQuestionsCount';
import { clearAllData } from '../src/services/database';
import { useAppStore } from '../src/store/useAppStore';
import { usePreferencesStore } from '../src/store/usePreferencesStore';
import { COLORS } from '../src/utils/constants';

export default function DashboardScreen() {
  const { isInitializing, setInitializing } = useAppStore();
  const { isInitialized, error: dbError } = useDatabase();
  const { loadPreferences, isLoaded } = usePreferencesStore();
  const {
    overallStats,
    predictedPassRate,
    volumeDisplayStats,
    loading: statsLoading,
    error: statsError,
    refreshStats
  } = useStatistics();
  const { count: wrongQuestionsCount, refreshCount: refreshWrongQuestionsCount } = useWrongQuestionsCount();
  const [loading, setLoading] = useState(true);
  const [examDateModalVisible, setExamDateModalVisible] = useState(false);



  useEffect(() => {
    const initialize = async () => {
      // Load preferences on app start
      if (!isLoaded) {
        await loadPreferences();
      }
      
      if (isInitialized && !dbError) {
        setInitializing(false);
        setLoading(false);
      } else if (dbError) {
        setLoading(false);
        Alert.alert('數據庫錯誤', dbError);
      }
    };
    
    initialize();
  }, [isInitialized, dbError, setInitializing, isLoaded, loadPreferences]);

  // Show error if statistics failed to load
  useEffect(() => {
    if (statsError) {
      Alert.alert('統計數據錯誤', statsError);
    }
  }, [statsError]);

  // Refresh statistics when screen comes into focus (e.g., returning from practice)
  useFocusEffect(
    useCallback(() => {
      if (isInitialized && !dbError) {
        refreshStats();
        refreshWrongQuestionsCount();
      }
    }, [isInitialized, dbError, refreshStats, refreshWrongQuestionsCount])
  );

  const handleStartExam = () => {
    router.push('/exam');
  };

  const handleStartWrongQuestionsReview = () => {
    router.push('/review');
  };

  const handleClearAllData = async () => {
    try {
      await clearAllData();
      // Refresh stats after clearing data
      refreshStats();
      refreshWrongQuestionsCount();
    } catch {
      // Error already handled in clearAllData function
    }
  };

  // Show fallback if no data is available
  if (!overallStats || !predictedPassRate || !volumeDisplayStats) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1 justify-center items-center p-8">
          <Text className="text-18 font-semibold text-text text-center mb-2">無法載入統計數據</Text>
          <Text className="text-14 text-text-secondary text-center">請檢查您的網路連接或稍後再試</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <StatusBar style="auto" backgroundColor={COLORS.BACKGROUND} />

      <ScrollView
        contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 20 }}
        showsVerticalScrollIndicator={false}
      >
        {/* 1. 歡迎信息 */}
        <View className="mb-5">
          <View className="flex-row items-center justify-between">
            <Text className="text-26 font-bold text-text tracking-wide">澳門駕駛理論考試</Text>
            <View className="flex-row items-center">
              <TouchableOpacity
                onPress={() => setExamDateModalVisible(true)}
                className="p-2 mr-2"
                activeOpacity={0.7}
              >
                <Ionicons name="calendar-outline" size={24} color={COLORS.TEXT} />
              </TouchableOpacity>
              {(isInitializing || loading || statsLoading) && (
                <ActivityIndicator
                  size="small"
                  color={COLORS.TEXT}
                />
              )}
            </View>
          </View>
        </View>

        {/* 2. 頂部狀態儀表板 */}
        <TopStatusDashboard
          stats={overallStats}
          prediction={predictedPassRate}
          userName="學員"
        />

        {/* 3. 主要操作按鈕 */}
        <ActionButtons
          onStartExam={handleStartExam}
          onStartWrongQuestionsReview={handleStartWrongQuestionsReview}
          wrongQuestionsCount={wrongQuestionsCount}
        />

        {/* 4. 各冊進度 */}
        <View className="mt-7">
          <Text className="text-22 font-bold text-text mb-5 tracking-wide">按冊練習</Text>
          <View className="flex-row flex-wrap justify-between gap-3">
            {volumeDisplayStats.map((stats) => (
              <View key={stats.volume} className="w-[48%]">
                <VolumePracticeCard
                  volumeStats={stats}
                />
              </View>
            ))}
          </View>
        </View>

        {/* Clear All Data Button */}
        {/* <View style={styles.clearDataSection}>
          <TouchableOpacity
            style={styles.clearDataButton}
            onPress={handleClearAllData}
            activeOpacity={0.7}
          >
            <MaterialIcons name="delete-outline" size={20} color={COLORS.ERROR} />
            <Text style={styles.clearDataText}>清除所有數據</Text>
          </TouchableOpacity>
        </View> */}
      </ScrollView>

      {/* Exam Date Modal */}
      <ExamDateModal
        visible={examDateModalVisible}
        onClose={() => setExamDateModalVisible(false)}
      />
    </SafeAreaView>
  );
}

