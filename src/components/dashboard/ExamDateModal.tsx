import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import React, { useEffect, useState } from 'react';
import { Platform, Text, TouchableOpacity, View } from 'react-native';
import { usePreferencesStore } from '../../store/usePreferencesStore';
import { COLORS } from '../../utils/constants';
import { Modal } from '../common/Modal';

interface ExamDateModalProps {
  visible: boolean;
  onClose: () => void;
}

export function ExamDateModal({ visible, onClose }: ExamDateModalProps) {
  const { examDate, updateExamDate } = usePreferencesStore();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  useEffect(() => {
    if (visible && examDate) {
      setSelectedDate(new Date(examDate));
    } else if (visible && !examDate) {
      setSelectedDate(null);
    }
  }, [visible, examDate]);

  const handleDateChange = (event: DateTimePickerEvent, date?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (date) {
      setSelectedDate(date);
    }
  };

  const handleSave = async () => {
    try {
      const dateString = selectedDate ? selectedDate.toISOString().split('T')[0] : null;
      await updateExamDate(dateString);
      onClose();
    } catch (error) {
      console.error('Failed to save exam date:', error);
    }
  };

  const handleClear = async () => {
    try {
      await updateExamDate(null);
      setSelectedDate(null);
    } catch (error) {
      console.error('Failed to clear exam date:', error);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return '未設定';
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const calculateDaysRemaining = (date: Date | null) => {
    if (!date) return null;

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const examDate = new Date(date);
    examDate.setHours(0, 0, 0, 0);

    const diffTime = examDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getDaysRemainingColor = (days: number | null) => {
    if (!days) return COLORS.TEXT_SECONDARY;
    if (days <= 7) return COLORS.ERROR;
    if (days <= 30) return COLORS.WARNING;
    return COLORS.SUCCESS;
  };

  const daysRemaining = calculateDaysRemaining(selectedDate);

  return (
    <>
      <Modal
        visible={visible && !showDatePicker}
        onClose={onClose}
        title="設定考試日期"
        primaryAction={{
          title: '儲存',
          onPress: handleSave,
        }}
        secondaryAction={{
          title: '清除',
          onPress: handleClear,
        }}
      >
        <View className="py-6 space-y-6">
          {/* Current Date Display */}
          <View>
            <Text className="text-16 font-medium text-text mb-3">目前設定日期</Text>
            <View className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <Text className="text-18 font-semibold text-text text-center mb-1">
                {formatDate(selectedDate)}
              </Text>
              {daysRemaining !== null && (
                <Text
                  className="text-14 text-center"
                  style={{ color: getDaysRemainingColor(daysRemaining) }}
                >
                  {daysRemaining < 0
                    ? `考試已過期 ${Math.abs(daysRemaining)} 天`
                    : daysRemaining === 0
                    ? '今天考試！'
                    : `還有 ${daysRemaining} 天`
                  }
                </Text>
              )}
            </View>
          </View>

          {/* Date Picker Button */}
          <TouchableOpacity
            onPress={() => setShowDatePicker(true)}
            className="bg-primary rounded-lg py-4 px-6"
            activeOpacity={0.7}
          >
            <Text className="text-16 font-medium text-white text-center">
              {selectedDate ? '修改考試日期' : '選擇考試日期'}
            </Text>
          </TouchableOpacity>

          {/* Helper Text */}
          <View className="pt-2">
            <Text className="text-14 text-text-secondary text-center leading-5">
              設定考試日期後，您可以在主畫面和 widget 中看到倒數計時
            </Text>
          </View>
        </View>
      </Modal>

      {/* Date Picker Modal for iOS */}
      {showDatePicker && Platform.OS === 'ios' && (
        <Modal
          visible={showDatePicker}
          onClose={() => setShowDatePicker(false)}
          title="選擇日期"
          primaryAction={{
            title: '確認',
            onPress: () => setShowDatePicker(false),
          }}
        >
          <View className="py-6 items-center">
            <DateTimePicker
              value={selectedDate || new Date()}
              mode="date"
              display="spinner"
              minimumDate={new Date()}
              onChange={handleDateChange}
              style={{ width: 300, height: 200 }}
            />
          </View>
        </Modal>
      )}

      {/* Date Picker for Android */}
      {showDatePicker && Platform.OS === 'android' && (
        <DateTimePicker
          value={selectedDate || new Date()}
          mode="date"
          display="default"
          minimumDate={new Date()}
          onChange={handleDateChange}
        />
      )}
    </>
  );
}

